import json
import time
import pytest
from vm import ContractTester

vcloudClient = ContractTester(
    wasmName="vcloud_db",
)


@pytest.fixture(autouse=True)
def register_contract():
    vcloudClient.constructor()


def create_test_user_service(service_id="test_service_1", address="0xtest_address_1", provider="test_provider_1", status="active"):
    """Create a test UserService with realistic data"""
    unique_suffix = str(int(time.time() * 1000000))[-6:]
    return {
        "_id": f"{service_id}_{unique_suffix}",
        "duration": 3600,
        "amount": 100.0,
        "publicKey": "0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d",
        "provider": provider,
        "providerAddress": "0xprovider_address_123",
        "address": address,
        "serviceID": "service_type_compute",
        "serviceActivated": True,
        "status": status,
        "serviceOptions": {
            "cpu": "4",
            "memory": "8GB",
            "storage": "100GB"
        },
        "createdAt": 0,
        "updatedAt": 0,
        "deletedAt": 0,
        "endAt": 0,
        "serviceActivateTS": 0,
        "serviceRunningTS": 0,
        "serviceAbortTS": 0,
        "serviceDoneTS": 0,
        "serviceRefundTS": 0,
        "service": "compute",
        "createdAddr": address,
        "labelHash": "0x123456789abcdef"
    }


def create_test_order(order_id="test_order_1", address="0xtest_address_1", provider="test_provider_1", status="pending"):
    """Create a test Order with realistic data"""
    unique_suffix = str(int(time.time() * 1000000))[-6:]
    return {
        "_id": f"{order_id}_{unique_suffix}",
        "createdAt": 0,
        "updatedAt": 0,
        "deletedAt": 0,
        "type": "service_purchase",
        "amount": 500.0,
        "amountPaid": 0.0,
        "provider": provider,
        "address": address,
        "recipient": "0xrecipient_address",
        "status": status,
        "lastPaymentTS": 0,
        "paidTS": 0,
        "filedTS": 0,
        "publicKey": "0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d",
        "userServiceIDs": [],
        "items": []
    }


def test_unified_insert_user_service():
    """Test unified insert function for user services"""
    service = create_test_user_service("unified_insert_test")
    service_json = json.dumps(service)
    
    # Test unified insert
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is None
    assert result == service["_id"]


def test_unified_insert_order():
    """Test unified insert function for orders"""
    order = create_test_order("unified_insert_order_test")
    order_json = json.dumps(order)
    
    # Test unified insert
    result, err = vcloudClient.execute("insert", str, "order", order_json)
    assert err is None
    assert result == order["_id"]


def test_unified_find_user_service():
    """Test unified find function for user services"""
    # Create test service
    service = create_test_user_service("unified_find_test", "0xfind_addr", "find_provider", "active")
    service_json = json.dumps(service)
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is None
    
    # Test unified find
    filter_params = {
        "address": "0xfind_addr",
        "status": "active",
        "limit": 10,
        "offset": 0
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None
    
    found_services = json.loads(result)
    assert len(found_services) >= 1
    assert any(s["_id"] == service["_id"] for s in found_services)


def test_unified_find_order():
    """Test unified find function for orders"""
    # Create test order
    order = create_test_order("unified_find_order_test", "0xfind_order_addr", "find_order_provider", "pending")
    order_json = json.dumps(order)
    result, err = vcloudClient.execute("insert", str, "order", order_json)
    assert err is None
    
    # Test unified find
    filter_params = {
        "address": "0xfind_order_addr",
        "statuses": ["pending"],
        "limit": 10,
        "offset": 0
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "order", filter_json)
    assert err is None
    
    found_orders = json.loads(result)
    assert len(found_orders) >= 1
    assert any(o["_id"] == order["_id"] for o in found_orders)


def test_unified_count_user_service():
    """Test unified count function for user services"""
    # Create test services
    services = [
        create_test_user_service("count_test_1", "0xcount_addr", "count_provider", "active"),
        create_test_user_service("count_test_2", "0xcount_addr", "count_provider", "active"),
    ]
    
    for service in services:
        service_json = json.dumps(service)
        result, err = vcloudClient.execute("insert", str, "user_service", service_json)
        assert err is None
    
    # Test unified count
    filter_params = {
        "address": "0xcount_addr",
        "status": "active"
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("count", str, "user_service", filter_json)
    assert err is None
    
    count = int(result)
    assert count >= 2


def test_unified_count_order():
    """Test unified count function for orders"""
    # Create test orders
    orders = [
        create_test_order("count_order_test_1", "0xcount_order_addr", "count_order_provider", "pending"),
        create_test_order("count_order_test_2", "0xcount_order_addr", "count_order_provider", "pending"),
    ]
    
    for order in orders:
        order_json = json.dumps(order)
        result, err = vcloudClient.execute("insert", str, "order", order_json)
        assert err is None
    
    # Test unified count
    filter_params = {
        "address": "0xcount_order_addr",
        "statuses": ["pending"]
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("count", str, "order", filter_json)
    assert err is None
    
    count = int(result)
    assert count >= 2


def test_unified_insert_many_user_service():
    """Test unified insert_many function for user services"""
    services = [
        create_test_user_service("insert_many_test_1", "0xinsert_many_addr", "insert_many_provider", "active"),
        create_test_user_service("insert_many_test_2", "0xinsert_many_addr", "insert_many_provider", "active"),
    ]
    
    services_json = json.dumps(services)
    result, err = vcloudClient.execute("insert_many", str, "user_service", services_json)
    assert err is None
    
    batch_result = json.loads(result)
    assert batch_result["created"] == 2
    assert len(batch_result["errors"]) == 0


def test_unified_insert_many_order():
    """Test unified insert_many function for orders"""
    orders = [
        create_test_order("insert_many_order_test_1", "0xinsert_many_order_addr", "insert_many_order_provider", "pending"),
        create_test_order("insert_many_order_test_2", "0xinsert_many_order_addr", "insert_many_order_provider", "pending"),
    ]
    
    orders_json = json.dumps(orders)
    result, err = vcloudClient.execute("insert_many", str, "order", orders_json)
    assert err is None
    
    batch_result = json.loads(result)
    assert batch_result["created"] == 2
    assert len(batch_result["errors"]) == 0


def test_unified_delete_user_service():
    """Test unified delete function for user services"""
    # Create test service
    service = create_test_user_service("delete_test", "0xdelete_addr", "delete_provider", "active")
    service_json = json.dumps(service)
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is None
    
    # Test unified delete
    filter_params = {
        "ids": [service["_id"]]
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.execute("delete", str, "user_service", filter_json)
    assert err is None
    
    delete_result = json.loads(result)
    assert delete_result["deleted"] == 1


def test_unified_delete_order():
    """Test unified delete function for orders"""
    # Create test order
    order = create_test_order("delete_order_test", "0xdelete_order_addr", "delete_order_provider", "pending")
    order_json = json.dumps(order)
    result, err = vcloudClient.execute("insert", str, "order", order_json)
    assert err is None
    
    # Test unified delete
    filter_params = {
        "_ids": [order["_id"]]
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.execute("delete", str, "order", filter_json)
    assert err is None
    
    delete_result = json.loads(result)
    assert delete_result["deleted"] == 1


def test_unified_update_user_service():
    """Test unified update function for user services"""
    # Create test service
    service = create_test_user_service("update_unified_test", "0xupdate_addr", "update_provider", "active")
    service_json = json.dumps(service)
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is None

    # Update the service
    service["amount"] = 200.0
    service["status"] = "suspended"
    updated_service_json = json.dumps(service)
    result, err = vcloudClient.execute("update", str, "user_service", updated_service_json)
    assert err is None

    # Verify the update using unified get
    result, err = vcloudClient.executeReadOnly("get", str, "user_service", service["_id"])
    assert err is None
    updated_service = json.loads(result)
    assert updated_service["amount"] == 200.0
    assert updated_service["status"] == "suspended"


def test_unified_update_order():
    """Test unified update function for orders"""
    # Create test order
    order = create_test_order("update_unified_order_test", "0xupdate_order_addr", "update_order_provider", "pending")
    order_json = json.dumps(order)
    result, err = vcloudClient.execute("insert", str, "order", order_json)
    assert err is None

    # Update the order
    order["amount"] = 750.0
    order["status"] = "paid"
    order["amountPaid"] = 750.0
    updated_order_json = json.dumps(order)
    result, err = vcloudClient.execute("update", str, "order", updated_order_json)
    assert err is None

    # Verify the update using unified get
    result, err = vcloudClient.executeReadOnly("get", str, "order", order["_id"])
    assert err is None
    updated_order = json.loads(result)
    assert updated_order["amount"] == 750.0
    assert updated_order["status"] == "paid"
    assert updated_order["amountPaid"] == 750.0


def test_unified_get_user_service():
    """Test unified get function for user services"""
    # Create test service
    service = create_test_user_service("get_unified_test", "0xget_addr", "get_provider", "active")
    service_json = json.dumps(service)
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is None

    # Test unified get
    result, err = vcloudClient.executeReadOnly("get", str, "user_service", service["_id"])
    assert err is None
    retrieved_service = json.loads(result)
    assert retrieved_service["_id"] == service["_id"]
    assert retrieved_service["amount"] == service["amount"]
    assert retrieved_service["provider"] == service["provider"]

    # Test non-existent service
    result, err = vcloudClient.executeReadOnly("get", str, "user_service", "non_existent_id")
    assert err is not None
    assert "not found" in str(err)


def test_unified_get_order():
    """Test unified get function for orders"""
    # Create test order
    order = create_test_order("get_unified_order_test", "0xget_order_addr", "get_order_provider", "pending")
    order_json = json.dumps(order)
    result, err = vcloudClient.execute("insert", str, "order", order_json)
    assert err is None

    # Test unified get
    result, err = vcloudClient.executeReadOnly("get", str, "order", order["_id"])
    assert err is None
    retrieved_order = json.loads(result)
    assert retrieved_order["_id"] == order["_id"]
    assert retrieved_order["amount"] == order["amount"]
    assert retrieved_order["provider"] == order["provider"]
    assert retrieved_order["status"] == order["status"]

    # Test non-existent order
    result, err = vcloudClient.executeReadOnly("get", str, "order", "non_existent_order_id")
    assert err is not None
    assert "not found" in str(err)


def test_unified_delete_many_user_service():
    """Test unified delete_many function for user services"""
    # Create test services
    services = [
        create_test_user_service("delete_many_test_1", "0xdelete_many_addr", "delete_many_provider", "active"),
        create_test_user_service("delete_many_test_2", "0xdelete_many_addr", "delete_many_provider", "active"),
        create_test_user_service("delete_many_test_3", "0xdelete_many_addr", "delete_many_provider", "inactive"),
    ]

    for service in services:
        service_json = json.dumps(service)
        result, err = vcloudClient.execute("insert", str, "user_service", service_json)
        assert err is None

    # Test unified delete_many with status filter
    filter_params = {
        "address": "0xdelete_many_addr",
        "status": "active"
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.execute("delete_many", str, "user_service", filter_json)
    assert err is None

    delete_result = json.loads(result)
    assert delete_result["deleted"] == 2  # Should delete 2 active services


def test_unified_delete_many_order():
    """Test unified delete_many function for orders"""
    # Create test orders
    orders = [
        create_test_order("delete_many_order_test_1", "0xdelete_many_order_addr", "delete_many_order_provider", "pending"),
        create_test_order("delete_many_order_test_2", "0xdelete_many_order_addr", "delete_many_order_provider", "pending"),
        create_test_order("delete_many_order_test_3", "0xdelete_many_order_addr", "delete_many_order_provider", "paid"),
    ]

    for order in orders:
        order_json = json.dumps(order)
        result, err = vcloudClient.execute("insert", str, "order", order_json)
        assert err is None

    # Test unified delete_many with status filter
    filter_params = {
        "order_address": "0xdelete_many_order_addr",
        "statuses": ["pending"]
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.execute("delete_many", str, "order", filter_json)
    assert err is None

    delete_result = json.loads(result)
    assert delete_result["deleted"] == 2  # Should delete 2 pending orders


def test_unified_complex_queries():
    """Test complex queries with multiple filters"""
    # Create test data with variety
    services = [
        create_test_user_service("complex_1", "0xcomplex_addr1", "provider1", "active"),
        create_test_user_service("complex_2", "0xcomplex_addr1", "provider2", "inactive"),
        create_test_user_service("complex_3", "0xcomplex_addr2", "provider1", "active"),
        create_test_user_service("complex_4", "0xcomplex_addr2", "provider2", "suspended"),
    ]

    # Modify amounts for variety
    services[0]["amount"] = 100.0
    services[1]["amount"] = 200.0
    services[2]["amount"] = 300.0
    services[3]["amount"] = 150.0

    for service in services:
        service_json = json.dumps(service)
        result, err = vcloudClient.execute("insert", str, "user_service", service_json)
        assert err is None

    # Test complex query: address + status + provider
    filter_params = {
        "address": "0xcomplex_addr1",
        "status": "active",
        "provider": "provider1",
        "limit": 10,
        "offset": 0
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None

    found_services = json.loads(result)
    assert len(found_services) == 1
    assert found_services[0]["address"] == "0xcomplex_addr1"
    assert found_services[0]["status"] == "active"
    assert found_services[0]["provider"] == "provider1"


def test_unified_pagination():
    """Test pagination with unified interface"""
    # Create multiple test services
    services = []
    for i in range(5):
        service = create_test_user_service(f"pagination_test_{i}", "0xpagination_addr", "pagination_provider", "active")
        services.append(service)
        service_json = json.dumps(service)
        result, err = vcloudClient.execute("insert", str, "user_service", service_json)
        assert err is None

    # Test pagination - first page
    filter_params = {
        "address": "0xpagination_addr",
        "limit": 2,
        "offset": 0
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None

    page1_services = json.loads(result)
    assert len(page1_services) == 2

    # Test pagination - second page
    filter_params["offset"] = 2
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None

    page2_services = json.loads(result)
    assert len(page2_services) == 2

    # Verify no overlap between pages
    page1_ids = {s["_id"] for s in page1_services}
    page2_ids = {s["_id"] for s in page2_services}
    assert len(page1_ids.intersection(page2_ids)) == 0


def test_unified_error_handling():
    """Test error handling in unified interface"""
    # Test invalid JSON
    result, err = vcloudClient.executeReadOnly("find", str, "user_service", "invalid_json")
    assert err is not None

    # Test empty table name
    result, err = vcloudClient.executeReadOnly("find", str, "", "{}")
    assert err is not None

    # Test invalid operation
    result, err = vcloudClient.execute("invalid_operation", str, "user_service", "{}")
    assert err is not None

    # Test get with empty ID
    result, err = vcloudClient.executeReadOnly("get", str, "user_service", "")
    assert err is not None


def test_unified_data_validation():
    """Test data validation in unified interface"""
    # Test creating service with empty ID
    invalid_service = create_test_user_service("validation_test")
    invalid_service["_id"] = ""
    invalid_service_json = json.dumps(invalid_service)
    result, err = vcloudClient.execute("insert", str, "user_service", invalid_service_json)
    assert err is not None
    assert "ID cannot be empty" in str(err)

    # Test creating order with empty ID
    invalid_order = create_test_order("validation_order_test")
    invalid_order["_id"] = ""
    invalid_order_json = json.dumps(invalid_order)
    result, err = vcloudClient.execute("insert", str, "order", invalid_order_json)
    assert err is not None
    assert "ID cannot be empty" in str(err)


def test_unified_lifecycle():
    """Test complete lifecycle using unified interface"""
    # Create service using unified interface
    service = create_test_user_service("lifecycle_unified_test", "0xlifecycle_addr", "lifecycle_provider", "active")
    service_json = json.dumps(service)
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is None
    service_id = result

    # Get service using unified interface
    result, err = vcloudClient.executeReadOnly("get", str, "user_service", service_id)
    assert err is None
    retrieved_service = json.loads(result)
    assert retrieved_service["status"] == "active"

    # Update service using unified interface
    retrieved_service["status"] = "suspended"
    retrieved_service["amount"] = 250.0
    updated_service_json = json.dumps(retrieved_service)
    result, err = vcloudClient.execute("update", str, "user_service", updated_service_json)
    assert err is None

    # Find service using unified interface
    filter_params = {
        "address": "0xlifecycle_addr",
        "status": "suspended"
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None
    found_services = json.loads(result)
    assert len(found_services) >= 1
    assert any(s["_id"] == service_id for s in found_services)

    # Count services using unified interface
    result, err = vcloudClient.executeReadOnly("count", str, "user_service", filter_json)
    assert err is None
    count = int(result)
    assert count >= 1

    # Delete service using unified interface
    delete_filter = {"ids": [service_id]}
    delete_filter_json = json.dumps(delete_filter)
    result, err = vcloudClient.execute("delete", str, "user_service", delete_filter_json)
    assert err is None
    delete_result = json.loads(result)
    assert delete_result["deleted"] == 1

    # Verify service is deleted (hard delete)
    result, err = vcloudClient.executeReadOnly("get", str, "user_service", service_id)
    assert err is not None
    assert "not found" in str(err)


def test_unsupported_table_name():
    """Test error handling for unsupported table names"""
    # Test with unsupported table name
    result, err = vcloudClient.executeReadOnly("find", str, "unsupported_table", "{}")
    assert err is not None
    assert "Unsupported table name" in str(err)
